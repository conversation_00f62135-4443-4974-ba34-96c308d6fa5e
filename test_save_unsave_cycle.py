#!/usr/bin/env python3
"""
Test the complete save/unsave cycle to verify it works correctly
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_save_unsave_cycle():
    # Login
    login_data = {"email": "<EMAIL>", "password": "password123"}
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    data = response.json()
    token = data.get('token')
    
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    # Get a recipe to test with
    response = requests.post(f"{BASE_URL}/api/recommend", 
                           json={"ingredients": ["chicken"]}, 
                           headers=headers)
    recipes = response.json().get('recipes', [])
    recipe_id = recipes[0].get('id')
    
    print(f"Testing with recipe ID: {recipe_id}")
    
    # Step 1: Check initial saved recipes
    print("\n=== Step 1: Check initial saved recipes ===")
    response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
    print(f"Initial saved recipes response: {response.status_code}")
    if response.status_code == 200:
        saved_recipes = response.json().get('recipes', [])
        print(f"Initial saved recipes count: {len(saved_recipes)}")
        initial_ids = [r.get('id') for r in saved_recipes]
        print(f"Initial recipe IDs: {initial_ids}")
    else:
        print(f"Error getting saved recipes: {response.text}")
        return
    
    # Step 2: Save the recipe
    print(f"\n=== Step 2: Save recipe {recipe_id} ===")
    response = requests.post(f"{BASE_URL}/api/recipe/{recipe_id}/save", headers=headers)
    print(f"Save response: {response.status_code} - {response.text}")
    
    # Step 3: Check saved recipes after saving
    print("\n=== Step 3: Check saved recipes after saving ===")
    response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
    print(f"Saved recipes response: {response.status_code}")
    if response.status_code == 200:
        saved_recipes = response.json().get('recipes', [])
        print(f"Saved recipes count after saving: {len(saved_recipes)}")
        after_save_ids = [r.get('id') for r in saved_recipes]
        print(f"Recipe IDs after saving: {after_save_ids}")
        
        # Check if our recipe is in the list
        if recipe_id in after_save_ids:
            print(f"✅ Recipe {recipe_id} is correctly saved!")
        else:
            print(f"❌ Recipe {recipe_id} is NOT in saved recipes!")
    else:
        print(f"Error getting saved recipes: {response.text}")
        return
    
    # Step 4: Unsave the recipe
    print(f"\n=== Step 4: Unsave recipe {recipe_id} ===")
    response = requests.post(f"{BASE_URL}/api/recipe/{recipe_id}/unsave", headers=headers)
    print(f"Unsave response: {response.status_code} - {response.text}")
    
    # Step 5: Check saved recipes after unsaving
    print("\n=== Step 5: Check saved recipes after unsaving ===")
    response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
    print(f"Saved recipes response: {response.status_code}")
    if response.status_code == 200:
        saved_recipes = response.json().get('recipes', [])
        print(f"Saved recipes count after unsaving: {len(saved_recipes)}")
        after_unsave_ids = [r.get('id') for r in saved_recipes]
        print(f"Recipe IDs after unsaving: {after_unsave_ids}")
        
        # Check if our recipe is removed from the list
        if recipe_id not in after_unsave_ids:
            print(f"✅ Recipe {recipe_id} is correctly removed!")
        else:
            print(f"❌ Recipe {recipe_id} is STILL in saved recipes!")
    else:
        print(f"Error getting saved recipes: {response.text}")

if __name__ == "__main__":
    test_save_unsave_cycle()
