#!/usr/bin/env python3
"""
Test JWT token validation
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_jwt_validation():
    print("Testing JWT token validation...")
    
    # Test with no token
    print("\n1. Testing with no Authorization header...")
    response = requests.get(f"{BASE_URL}/api/recipes/saved")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
    
    # Test with invalid token
    print("\n2. Testing with invalid token...")
    headers = {'Authorization': 'Bearer invalid_token_here'}
    response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
    
    # Test with valid token
    print("\n3. Getting valid token...")
    login_data = {"email": "<EMAIL>", "password": "password123"}
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    print(f"Login status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        token = data.get('token')
        print(f"Got token: {token[:50]}...")
        
        print("\n4. Testing with valid token...")
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
    else:
        print(f"Login failed: {response.text}")

if __name__ == "__main__":
    test_jwt_validation()
