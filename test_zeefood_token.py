#!/usr/bin/env python3
"""
Test with zeefood12's actual token
"""

import requests

BASE_URL = "http://localhost:5000"

def test_zeefood_token():
    # Use the exact token from the browser
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************.5Eudk2DqOnxggfeBFfXBqR5NxFzX7sUiD7DjSdfrOOQ"
    
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    print("Testing with zeefood12's token...")
    print(f"Token: {token[:50]}...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 500:
            print("\n❌ 500 Error confirmed - there's a server-side error")
            print("This is likely the community recipe issue I identified earlier")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_zeefood_token()
