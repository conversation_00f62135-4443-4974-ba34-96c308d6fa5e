#!/usr/bin/env python3
"""
Test if the frontend can reach the backend
"""

import requests

BASE_URL = "http://localhost:5000"

def test_frontend_connectivity():
    print("Testing frontend connectivity...")
    
    # Test if the save-recipe page loads
    print("\n1. Testing save-recipe page...")
    try:
        response = requests.get(f"{BASE_URL}/save-recipe")
        print(f"Save-recipe page status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Save-recipe page loads successfully")
        else:
            print(f"❌ Save-recipe page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error loading save-recipe page: {e}")
    
    # Test if the API health endpoint works
    print("\n2. Testing API health...")
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        print(f"Health status: {response.status_code}")
        if response.status_code == 200:
            print("✅ API is healthy")
        else:
            print(f"❌ API health failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking API health: {e}")
    
    # Test if we can reach the auth endpoint
    print("\n3. Testing auth endpoint...")
    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", json={"email": "test", "password": "test"})
        print(f"Auth endpoint status: {response.status_code}")
        if response.status_code in [400, 401]:  # Expected for invalid credentials
            print("✅ Auth endpoint is reachable")
        else:
            print(f"Auth endpoint response: {response.text}")
    except Exception as e:
        print(f"❌ Error reaching auth endpoint: {e}")

if __name__ == "__main__":
    test_frontend_connectivity()
