#!/usr/bin/env python3
"""
Test saved recipes endpoint directly
"""

import requests

def test_saved_recipes_direct():
    print("Testing saved recipes endpoint...")
    
    # Use the exact token from the browser
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************.5Eudk2DqOnxggfeBFfXBqR5NxFzX7sUiD7DjSdfrOOQ"
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        print("Making request to /api/recipes/saved...")
        response = requests.get('http://localhost:5000/api/recipes/saved', headers=headers, timeout=10)
        print(f"Status: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'N/A')}")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            print(f"Response: {response.json()}")
        else:
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_saved_recipes_direct()
