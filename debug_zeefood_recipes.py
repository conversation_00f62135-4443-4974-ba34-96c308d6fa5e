#!/usr/bin/env python3
"""
Debug zeefood12's saved recipes to find the issue
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from pymongo import MongoClient
from bson import ObjectId

def debug_zeefood_recipes():
    # Connect to MongoDB
    client = MongoClient('mongodb://localhost:27017/')
    db = client['sisarasa']
    
    # Get zeefood12's user data
    user = db.users.find_one({'_id': ObjectId('682d6d668ed2680f1038026f')})
    
    if not user:
        print("User not found")
        return
    
    print(f"User: {user['name']} ({user['email']})")
    saved_recipe_ids = user.get('saved_recipes', [])
    print(f"Saved recipe IDs: {len(saved_recipe_ids)}")
    
    # Check each saved recipe
    for i, recipe_id in enumerate(saved_recipe_ids):
        print(f"\n{i+1}. Checking recipe ID: {recipe_id}")
        
        try:
            # Try to convert to ObjectId
            obj_id = ObjectId(recipe_id)
            print(f"   ObjectId conversion: OK")
            
            # Try to find the recipe
            recipe = db.recipes.find_one({'_id': obj_id})
            if recipe:
                print(f"   Recipe found: {recipe.get('name', 'No name')}")
                print(f"   Has original_id: {recipe.get('original_id', 'No original_id')}")
                
                # Check if all required fields exist
                required_fields = ['name', 'ingredients', 'steps', 'techniques', 'calorie_level']
                missing_fields = []
                for field in required_fields:
                    if field not in recipe:
                        missing_fields.append(field)
                
                if missing_fields:
                    print(f"   ❌ Missing fields: {missing_fields}")
                else:
                    print(f"   ✅ All required fields present")
            else:
                print(f"   ❌ Recipe not found in database")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    debug_zeefood_recipes()
