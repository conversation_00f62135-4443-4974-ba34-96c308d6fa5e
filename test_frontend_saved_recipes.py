#!/usr/bin/env python3
"""
Test the saved recipes API with the same user that's logged in the frontend
"""

import requests

BASE_URL = "http://localhost:5000"

def test_frontend_user():
    # Login with the same user as the frontend (zeefood12)
    login_data = {"email": "<EMAIL>", "password": "password123"}
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    print(f"Login status: {response.status_code}")
    
    if response.status_code != 200:
        print(f"Login failed: {response.text}")
        return
    
    data = response.json()
    token = data.get('token')
    print(f"Login successful, got token")
    
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    # Test the saved recipes API
    print("\nTesting saved recipes API...")
    response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
    print(f"Saved recipes status: {response.status_code}")
    print(f"Response: {response.text}")

if __name__ == "__main__":
    test_frontend_user()
