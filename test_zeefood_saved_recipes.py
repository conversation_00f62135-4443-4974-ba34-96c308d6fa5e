#!/usr/bin/env python3
"""
Test the saved recipes API directly with zeefood12's user ID
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from api.models.recipe import get_saved_recipes_for_user
from bson import ObjectId

def test_zeefood_saved_recipes():
    user_id = "682d6d668ed2680f1038026f"  # zeefood12's user ID
    
    print(f"Testing saved recipes for user: {user_id}")
    
    try:
        # Call the function directly
        recipes = get_saved_recipes_for_user(user_id)
        print(f"✅ Successfully retrieved {len(recipes)} saved recipes")
        
        # Check each recipe
        for i, recipe in enumerate(recipes):
            print(f"\n{i+1}. Recipe: {recipe.get('name', 'No name')}")
            print(f"   ID: {recipe.get('_id')}")
            print(f"   Original ID: {recipe.get('original_id', 'No original_id')}")
            print(f"   Has steps: {'steps' in recipe}")
            print(f"   Has techniques: {'techniques' in recipe}")
            print(f"   Has calorie_level: {'calorie_level' in recipe}")
            
            # Check if this is the problematic recipe
            if recipe.get('name') == 'NASI GORENG KRYSTAL':
                print(f"   ⚠️  This is the problematic community recipe")
                print(f"   Steps: {recipe.get('steps', 'MISSING')}")
                print(f"   Instructions: {recipe.get('instructions', 'MISSING')}")
                print(f"   Techniques: {recipe.get('techniques', 'MISSING')}")
                print(f"   Calorie level: {recipe.get('calorie_level', 'MISSING')}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_zeefood_saved_recipes()
