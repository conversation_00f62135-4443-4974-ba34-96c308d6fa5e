#!/usr/bin/env python3
"""
Test JWT token decoding
"""

import jwt
import json
from datetime import datetime

def test_jwt_decode():
    # Use the exact token from the browser
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************.5Eudk2DqOnxggfeBFfXBqR5NxFzX7sUiD7DjSdfrOOQ"
    
    print("Testing JWT token decoding...")
    
    try:
        # Decode without verification first to see the payload
        decoded = jwt.decode(token, options={"verify_signature": False})
        print(f"Token payload: {json.dumps(decoded, indent=2)}")
        
        # Check expiration
        exp = decoded.get('exp')
        if exp:
            exp_time = datetime.fromtimestamp(exp)
            now = datetime.now()
            print(f"Token expires: {exp_time}")
            print(f"Current time: {now}")
            print(f"Token expired: {exp_time < now}")
        
        # Check user ID
        user_id = decoded.get('sub')
        print(f"User ID from token: {user_id}")
        
    except Exception as e:
        print(f"❌ Error decoding token: {e}")

if __name__ == "__main__":
    test_jwt_decode()
