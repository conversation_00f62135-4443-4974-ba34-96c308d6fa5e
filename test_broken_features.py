#!/usr/bin/env python3
"""
Comprehensive test script to diagnose broken database-dependent features
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def get_auth_token():
    """Get authentication token for testing"""
    login_url = f"{BASE_URL}/api/auth/login"
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }

    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            result = response.json()
            return result.get('token')  # Changed from 'access_token' to 'token'
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_review_system(token):
    """Test review system functionality"""
    print("\n🔍 Testing Review System...")
    
    # Test recipe ID (using a known recipe from recommendations)
    recipe_id = "9293"  # Chicken Curry from previous tests
    
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    # Test 1: Add a review
    print("  1. Testing add review...")
    review_data = {
        "rating": 5,
        "review_text": "Amazing recipe! Very tasty and easy to follow."
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/recipe/{recipe_id}/review", 
                               json=review_data, headers=headers)
        print(f"     Add Review: {response.status_code} - {response.text[:100]}...")
        add_review_success = response.status_code == 200
    except Exception as e:
        print(f"     Add Review Error: {e}")
        add_review_success = False
    
    # Test 2: Get reviews for recipe
    print("  2. Testing get reviews...")
    try:
        response = requests.get(f"{BASE_URL}/api/recipe/{recipe_id}/reviews")
        print(f"     Get Reviews: {response.status_code} - {response.text[:100]}...")
        get_reviews_success = response.status_code == 200
    except Exception as e:
        print(f"     Get Reviews Error: {e}")
        get_reviews_success = False
    
    # Test 3: Get user's review for recipe
    print("  3. Testing get user review...")
    try:
        response = requests.get(f"{BASE_URL}/api/recipe/{recipe_id}/user-review", headers=headers)
        print(f"     Get User Review: {response.status_code} - {response.text[:100]}...")
        get_user_review_success = response.status_code == 200
    except Exception as e:
        print(f"     Get User Review Error: {e}")
        get_user_review_success = False
    
    return {
        'add_review': add_review_success,
        'get_reviews': get_reviews_success,
        'get_user_review': get_user_review_success
    }

def test_save_recipe_feature(token):
    """Test save recipe functionality"""
    print("\n💾 Testing Save Recipe Feature...")
    
    # Test recipe ID
    recipe_id = "9293"
    
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    # Test 1: Save a recipe
    print("  1. Testing save recipe...")
    try:
        response = requests.post(f"{BASE_URL}/api/recipe/{recipe_id}/save", headers=headers)
        print(f"     Save Recipe: {response.status_code} - {response.text[:100]}...")
        save_recipe_success = response.status_code == 200
    except Exception as e:
        print(f"     Save Recipe Error: {e}")
        save_recipe_success = False
    
    # Test 2: Get saved recipes
    print("  2. Testing get saved recipes...")
    try:
        response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
        print(f"     Get Saved Recipes: {response.status_code} - {response.text[:100]}...")
        get_saved_success = response.status_code == 200
        if get_saved_success:
            result = response.json()
            saved_count = result.get('count', 0)
            print(f"     Found {saved_count} saved recipes")
    except Exception as e:
        print(f"     Get Saved Recipes Error: {e}")
        get_saved_success = False
    
    # Test 3: Unsave recipe
    print("  3. Testing unsave recipe...")
    try:
        response = requests.post(f"{BASE_URL}/api/recipe/{recipe_id}/unsave", headers=headers)
        print(f"     Unsave Recipe: {response.status_code} - {response.text[:100]}...")
        unsave_recipe_success = response.status_code == 200
    except Exception as e:
        print(f"     Unsave Recipe Error: {e}")
        unsave_recipe_success = False
    
    return {
        'save_recipe': save_recipe_success,
        'get_saved': get_saved_success,
        'unsave_recipe': unsave_recipe_success
    }

def test_community_features(token):
    """Test community functionality"""
    print("\n👥 Testing Community Features...")
    
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    # Test 1: Submit community recipe
    print("  1. Testing submit community recipe...")
    recipe_data = {
        "name": "Test Community Recipe",
        "ingredients": ["test ingredient 1", "test ingredient 2"],
        "instructions": ["Step 1: Test step", "Step 2: Another test step"],
        "prep_time": 15,
        "cook_time": 30,
        "servings": 4,
        "cuisine": "Test Cuisine",
        "difficulty": "Easy"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/community/submit-recipe", 
                               json=recipe_data, headers=headers)
        print(f"     Submit Recipe: {response.status_code} - {response.text[:100]}...")
        submit_recipe_success = response.status_code in [200, 201]
    except Exception as e:
        print(f"     Submit Recipe Error: {e}")
        submit_recipe_success = False
    
    # Test 2: Get community recipes
    print("  2. Testing get community recipes...")
    try:
        response = requests.get(f"{BASE_URL}/api/community/recipes")
        print(f"     Get Community Recipes: {response.status_code} - {response.text[:100]}...")
        get_community_success = response.status_code == 200
        if get_community_success:
            result = response.json()
            recipe_count = result.get('count', 0)
            print(f"     Found {recipe_count} community recipes")
    except Exception as e:
        print(f"     Get Community Recipes Error: {e}")
        get_community_success = False
    
    # Test 3: Create community post
    print("  3. Testing create community post...")
    post_data = {
        "content": "This is a test community post about food waste reduction!"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/community/posts", 
                               json=post_data, headers=headers)
        print(f"     Create Post: {response.status_code} - {response.text[:100]}...")
        create_post_success = response.status_code in [200, 201]
    except Exception as e:
        print(f"     Create Post Error: {e}")
        create_post_success = False
    
    # Test 4: Get community posts
    print("  4. Testing get community posts...")
    try:
        response = requests.get(f"{BASE_URL}/api/community/posts", headers=headers)
        print(f"     Get Posts: {response.status_code} - {response.text[:100]}...")
        get_posts_success = response.status_code == 200
        if get_posts_success:
            result = response.json()
            post_count = len(result.get('posts', []))
            print(f"     Found {post_count} community posts")
    except Exception as e:
        print(f"     Get Posts Error: {e}")
        get_posts_success = False
    
    return {
        'submit_recipe': submit_recipe_success,
        'get_community_recipes': get_community_success,
        'create_post': create_post_success,
        'get_posts': get_posts_success
    }

def main():
    print("🧪 Diagnosing Broken Database-Dependent Features")
    print("=" * 60)
    
    # Get authentication token
    print("🔐 Getting authentication token...")
    token = get_auth_token()
    
    if not token:
        print("❌ Cannot proceed without authentication token")
        return
    
    print("✅ Authentication successful")
    
    # Test all features
    review_results = test_review_system(token)
    save_results = test_save_recipe_feature(token)
    community_results = test_community_features(token)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DIAGNOSIS SUMMARY")
    print("=" * 60)
    
    print("\n🔍 Review System:")
    for test, success in review_results.items():
        print(f"   {test}: {'✅ WORKING' if success else '❌ BROKEN'}")
    
    print("\n💾 Save Recipe Feature:")
    for test, success in save_results.items():
        print(f"   {test}: {'✅ WORKING' if success else '❌ BROKEN'}")
    
    print("\n👥 Community Features:")
    for test, success in community_results.items():
        print(f"   {test}: {'✅ WORKING' if success else '❌ BROKEN'}")
    
    # Overall assessment
    all_tests = list(review_results.values()) + list(save_results.values()) + list(community_results.values())
    working_count = sum(all_tests)
    total_count = len(all_tests)
    
    print(f"\n📈 Overall Status: {working_count}/{total_count} features working")
    
    if working_count == total_count:
        print("🎉 All features are working correctly!")
    else:
        print("⚠️  Some features need fixing. Check the detailed results above.")

if __name__ == "__main__":
    main()
