#!/usr/bin/env python3
"""
Test the saved recipes API endpoint directly
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_api_directly():
    print("Testing saved recipes API endpoint directly...")
    
    # First, let's try to create a test user with a known password
    print("\n1. Creating test user...")
    signup_data = {
        "name": "API Test User",
        "email": "<EMAIL>", 
        "password": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/signup", json=signup_data)
    print(f"Signup status: {response.status_code}")
    if response.status_code == 409:
        print("User already exists, proceeding with login...")
    
    # Login with the test user
    print("\n2. Logging in...")
    login_data = {"email": "<EMAIL>", "password": "testpass123"}
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    print(f"Login status: {response.status_code}")
    
    if response.status_code != 200:
        print(f"Login failed: {response.text}")
        return
    
    data = response.json()
    token = data.get('token')
    user_id = data.get('user', {}).get('id')
    print(f"Login successful, user ID: {user_id}")
    
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    # Test the saved recipes API
    print("\n3. Testing saved recipes API...")
    response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
    print(f"Saved recipes status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        recipes = data.get('recipes', [])
        print(f"✅ Successfully retrieved {len(recipes)} saved recipes")
        for recipe in recipes:
            print(f"  - ID: {recipe['id']}, Name: {recipe['name']}")
    else:
        print(f"❌ Error: {response.text}")
        
    # Now let's save a recipe and test again
    print("\n4. Saving a recipe...")
    response = requests.post(f"{BASE_URL}/api/recipe/1234/save", headers=headers)
    print(f"Save status: {response.status_code}")
    print(f"Save response: {response.text}")
    
    # Test saved recipes again
    print("\n5. Testing saved recipes after saving...")
    response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
    print(f"Saved recipes status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        recipes = data.get('recipes', [])
        print(f"✅ Successfully retrieved {len(recipes)} saved recipes")
        for recipe in recipes:
            print(f"  - ID: {recipe['id']}, Name: {recipe['name']}")
    else:
        print(f"❌ Error: {response.text}")

if __name__ == "__main__":
    test_api_directly()
