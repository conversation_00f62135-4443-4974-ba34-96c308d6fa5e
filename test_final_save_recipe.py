#!/usr/bin/env python3
"""
Final test to confirm save recipe functionality is working
"""

import requests

BASE_URL = "http://localhost:5000"

def test_final_save_recipe():
    # Login
    login_data = {"email": "<EMAIL>", "password": "password123"}
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    data = response.json()
    token = data.get('token')
    
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    print("=== Testing Save Recipe Functionality ===")
    
    # Test 1: Get saved recipes
    print("\n1. Getting current saved recipes...")
    response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        recipes = data.get('recipes', [])
        print(f"Current saved recipes: {len(recipes)}")
        for recipe in recipes:
            print(f"  - ID: {recipe['id']}, Name: {recipe['name']}")
    else:
        print(f"Error: {response.text}")
        return
    
    # Test 2: Save a new recipe
    print("\n2. Saving recipe 2600...")
    response = requests.post(f"{BASE_URL}/api/recipe/2600/save", headers=headers)
    print(f"Save status: {response.status_code}")
    print(f"Save response: {response.text}")
    
    # Test 3: Check saved recipes again
    print("\n3. Getting saved recipes after saving...")
    response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        recipes = data.get('recipes', [])
        print(f"Saved recipes after saving: {len(recipes)}")
        for recipe in recipes:
            print(f"  - ID: {recipe['id']}, Name: {recipe['name']}")
    
    # Test 4: Unsave the recipe
    print("\n4. Unsaving recipe 2600...")
    response = requests.post(f"{BASE_URL}/api/recipe/2600/unsave", headers=headers)
    print(f"Unsave status: {response.status_code}")
    print(f"Unsave response: {response.text}")
    
    # Test 5: Final check
    print("\n5. Final check of saved recipes...")
    response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        recipes = data.get('recipes', [])
        print(f"Final saved recipes: {len(recipes)}")
        for recipe in recipes:
            print(f"  - ID: {recipe['id']}, Name: {recipe['name']}")
    
    print("\n✅ Save recipe functionality test completed!")

if __name__ == "__main__":
    test_final_save_recipe()
