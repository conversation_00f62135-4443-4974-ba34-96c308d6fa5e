#!/usr/bin/env python3
"""
Test script to specifically test the save recipe functionality
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def get_auth_token():
    """Get authentication token"""
    print("🔐 Getting authentication token...")
    
    # Try to login with existing user
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                token = data.get('token')
                user_id = data.get('user', {}).get('id')
                print(f"✅ Login successful! User ID: {user_id}")
                return token, user_id
        
        print(f"❌ Login failed: {response.status_code} - {response.text}")
        return None, None
        
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None, None

def test_save_recipe_functionality():
    """Test the complete save recipe workflow"""
    print("\n🧪 Testing Save Recipe Functionality")
    print("=" * 60)
    
    # Get authentication token
    token, user_id = get_auth_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        return
    
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    # Test 1: Get some recipes first to have something to save
    print("\n1. Getting recipe recommendations to test saving...")
    try:
        response = requests.post(f"{BASE_URL}/api/recommend", 
                               json={"ingredients": ["chicken", "rice"]}, 
                               headers=headers)
        print(f"   Recommendations: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            recipes = data.get('recipes', [])
            if recipes:
                test_recipe = recipes[0]  # Use first recipe for testing
                recipe_id = test_recipe.get('id')
                recipe_name = test_recipe.get('name')
                print(f"   Found recipe to test: {recipe_name} (ID: {recipe_id})")
            else:
                print("   ❌ No recipes found to test with")
                return
        else:
            print(f"   ❌ Failed to get recommendations: {response.text}")
            return
    except Exception as e:
        print(f"   ❌ Error getting recommendations: {e}")
        return
    
    # Test 2: Save the recipe
    print(f"\n2. Testing save recipe (ID: {recipe_id})...")
    try:
        response = requests.post(f"{BASE_URL}/api/recipe/{recipe_id}/save", headers=headers)
        print(f"   Save Recipe: {response.status_code} - {response.text[:100]}...")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                print("   ✅ Recipe saved successfully!")
            else:
                print(f"   ❌ Save failed: {data.get('message')}")
                return
        else:
            print(f"   ❌ Save failed with status {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ Error saving recipe: {e}")
        return
    
    # Test 3: Get saved recipes
    print("\n3. Testing get saved recipes...")
    try:
        response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
        print(f"   Get Saved Recipes: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                saved_recipes = data.get('recipes', [])
                print(f"   ✅ Found {len(saved_recipes)} saved recipes")
                
                # Check if our test recipe is in the saved list
                found_test_recipe = False
                for saved_recipe in saved_recipes:
                    if saved_recipe.get('name') == recipe_name:
                        found_test_recipe = True
                        print(f"   ✅ Test recipe '{recipe_name}' found in saved recipes")
                        break
                
                if not found_test_recipe:
                    print(f"   ⚠️  Test recipe '{recipe_name}' not found in saved recipes")
            else:
                print(f"   ❌ Failed to get saved recipes: {data.get('message')}")
        else:
            print(f"   ❌ Failed to get saved recipes: {response.text}")
    except Exception as e:
        print(f"   ❌ Error getting saved recipes: {e}")
    
    # Test 4: Unsave the recipe
    print(f"\n4. Testing unsave recipe (ID: {recipe_id})...")
    try:
        response = requests.post(f"{BASE_URL}/api/recipe/{recipe_id}/unsave", headers=headers)
        print(f"   Unsave Recipe: {response.status_code} - {response.text[:100]}...")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                print("   ✅ Recipe unsaved successfully!")
            else:
                print(f"   ❌ Unsave failed: {data.get('message')}")
        else:
            print(f"   ❌ Unsave failed with status {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error unsaving recipe: {e}")
    
    # Test 5: Verify recipe was removed from saved list
    print("\n5. Verifying recipe was removed from saved list...")
    try:
        response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                saved_recipes = data.get('recipes', [])
                print(f"   Found {len(saved_recipes)} saved recipes after unsave")
                
                # Check if our test recipe is still in the saved list
                found_test_recipe = False
                for saved_recipe in saved_recipes:
                    if saved_recipe.get('name') == recipe_name:
                        found_test_recipe = True
                        break
                
                if not found_test_recipe:
                    print(f"   ✅ Test recipe '{recipe_name}' successfully removed from saved recipes")
                else:
                    print(f"   ⚠️  Test recipe '{recipe_name}' still found in saved recipes")
    except Exception as e:
        print(f"   ❌ Error verifying removal: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Save Recipe Test Complete!")

if __name__ == "__main__":
    test_save_recipe_functionality()
