#!/usr/bin/env python3
"""
Check what fields are actually stored in the database for saved recipes
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from pymongo import MongoClient
from bson import ObjectId

def check_db_recipe_fields():
    # Connect to MongoDB
    client = MongoClient('mongodb://localhost:27017/')
    db = client['sisarasa']
    
    # Get a sample recipe from the database
    recipe = db.recipes.find_one({'_id': ObjectId('6833ba2d6ec61c474c279eba')})
    
    if recipe:
        print("Recipe fields in database:")
        for key, value in recipe.items():
            if key == '_id':
                print(f"  {key}: {value}")
            elif isinstance(value, list) and len(value) > 3:
                print(f"  {key}: [list with {len(value)} items]")
            elif isinstance(value, str) and len(value) > 100:
                print(f"  {key}: [string with {len(value)} chars]")
            else:
                print(f"  {key}: {value}")
    else:
        print("Recipe not found")
    
    # Also check if there are any recipes with original_id field
    print("\nChecking for recipes with original_id field...")
    recipes_with_original_id = list(db.recipes.find({'original_id': {'$exists': True}}).limit(3))
    print(f"Found {len(recipes_with_original_id)} recipes with original_id field")
    
    if recipes_with_original_id:
        for i, recipe in enumerate(recipes_with_original_id):
            print(f"\nRecipe {i+1}:")
            print(f"  _id: {recipe['_id']}")
            print(f"  original_id: {recipe.get('original_id', 'N/A')}")
            print(f"  name: {recipe.get('name', 'N/A')}")

if __name__ == "__main__":
    check_db_recipe_fields()
