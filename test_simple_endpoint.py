#!/usr/bin/env python3
"""
Test simple endpoint to verify server connectivity
"""

import requests

def test_simple_endpoint():
    print("Testing simple endpoint...")
    
    try:
        response = requests.get('http://localhost:5000/api/test')
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            print("✅ Server is reachable")
        else:
            print("❌ Server returned error")
            
    except Exception as e:
        print(f"❌ Error connecting to server: {e}")

if __name__ == "__main__":
    test_simple_endpoint()
