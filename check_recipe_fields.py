#!/usr/bin/env python3
"""
Check what fields are available in saved recipe documents
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def check_recipe_fields():
    # Login
    login_data = {"email": "<EMAIL>", "password": "password123"}
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    data = response.json()
    token = data.get('token')
    
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    # Get saved recipes
    response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
    print(f"Saved recipes response: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        recipes = data.get('recipes', [])
        print(f"Found {len(recipes)} saved recipes")
        
        if recipes:
            print("\nFirst recipe fields:")
            first_recipe = recipes[0]
            for key, value in first_recipe.items():
                print(f"  {key}: {value}")
    else:
        print(f"Error: {response.text}")

if __name__ == "__main__":
    check_recipe_fields()
