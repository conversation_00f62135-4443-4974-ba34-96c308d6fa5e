#!/usr/bin/env python3
"""
Debug script to understand the unsave process
"""

import requests
import json
from pymongo import MongoClient
from bson import ObjectId

BASE_URL = "http://localhost:5000"

def get_auth_token():
    """Get authentication token"""
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                token = data.get('token')
                user_id = data.get('user', {}).get('id')
                return token, user_id
        return None, None
    except Exception as e:
        print(f"Login error: {e}")
        return None, None

def debug_unsave_process():
    """Debug the unsave process step by step"""
    print("🔍 Debugging Unsave Process")
    print("=" * 60)
    
    token, user_id = get_auth_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        return
    
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    # Connect to MongoDB directly to check data
    try:
        client = MongoClient('mongodb://localhost:27017/')
        db = client['sisarasa']
        print(f"✅ Connected to MongoDB")
    except Exception as e:
        print(f"❌ MongoDB connection failed: {e}")
        return
    
    # Get a test recipe
    print(f"\n1. Getting a test recipe...")
    try:
        response = requests.post(f"{BASE_URL}/api/recommend", 
                               json={"ingredients": ["chicken"]}, 
                               headers=headers)
        if response.status_code == 200:
            data = response.json()
            recipes = data.get('recipes', [])
            if recipes:
                test_recipe = recipes[0]
                recipe_id = test_recipe.get('id')
                recipe_name = test_recipe.get('name')
                print(f"   Test recipe: {recipe_name} (Original ID: {recipe_id})")
            else:
                print("   ❌ No recipes found")
                return
        else:
            print(f"   ❌ Failed to get recipes: {response.text}")
            return
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return
    
    # Check user's current saved recipes in database
    print(f"\n2. Checking user's saved recipes in database...")
    try:
        user_doc = db.users.find_one({'_id': ObjectId(user_id)})
        if user_doc:
            saved_recipes = user_doc.get('saved_recipes', [])
            print(f"   User has {len(saved_recipes)} saved recipes in database:")
            for i, saved_id in enumerate(saved_recipes):
                print(f"     {i+1}. {saved_id} (type: {type(saved_id)})")
        else:
            print(f"   ❌ User not found in database")
            return
    except Exception as e:
        print(f"   ❌ Error checking database: {e}")
        return
    
    # Save the test recipe
    print(f"\n3. Saving test recipe (Original ID: {recipe_id})...")
    try:
        response = requests.post(f"{BASE_URL}/api/recipe/{recipe_id}/save", headers=headers)
        print(f"   Save response: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Error saving: {e}")
        return
    
    # Check what was actually saved in database
    print(f"\n4. Checking what was saved in database...")
    try:
        # Check recipes collection
        recipe_doc = db.recipes.find_one({'original_id': int(recipe_id)})
        if recipe_doc:
            db_recipe_id = str(recipe_doc['_id'])
            print(f"   Found recipe in database:")
            print(f"     Database ID: {db_recipe_id}")
            print(f"     Original ID: {recipe_doc.get('original_id')}")
            print(f"     Name: {recipe_doc.get('name')}")
        else:
            print(f"   ❌ Recipe not found in database")
            return
        
        # Check user's saved recipes again
        user_doc = db.users.find_one({'_id': ObjectId(user_id)})
        if user_doc:
            saved_recipes = user_doc.get('saved_recipes', [])
            print(f"   User now has {len(saved_recipes)} saved recipes:")
            for i, saved_id in enumerate(saved_recipes):
                print(f"     {i+1}. {saved_id} (type: {type(saved_id)})")
                if str(saved_id) == db_recipe_id:
                    print(f"        ✅ This matches our test recipe!")
    except Exception as e:
        print(f"   ❌ Error checking database: {e}")
        return
    
    # Try to unsave
    print(f"\n5. Unsaving test recipe (Original ID: {recipe_id})...")
    try:
        response = requests.post(f"{BASE_URL}/api/recipe/{recipe_id}/unsave", headers=headers)
        print(f"   Unsave response: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Error unsaving: {e}")
        return
    
    # Check database after unsave
    print(f"\n6. Checking database after unsave...")
    try:
        user_doc = db.users.find_one({'_id': ObjectId(user_id)})
        if user_doc:
            saved_recipes = user_doc.get('saved_recipes', [])
            print(f"   User now has {len(saved_recipes)} saved recipes:")
            for i, saved_id in enumerate(saved_recipes):
                print(f"     {i+1}. {saved_id} (type: {type(saved_id)})")
                if str(saved_id) == db_recipe_id:
                    print(f"        ⚠️  Test recipe still here!")
            
            if db_recipe_id not in [str(s) for s in saved_recipes]:
                print(f"   ✅ Test recipe successfully removed!")
    except Exception as e:
        print(f"   ❌ Error checking database: {e}")

if __name__ == "__main__":
    debug_unsave_process()
