#!/usr/bin/env python3
"""
Check what users exist in the database
"""

import requests
import json

def test_signup():
    """Test creating a new user account"""
    signup_url = "http://localhost:5000/api/auth/signup"
    signup_data = {
        "name": "Test User",
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        response = requests.post(signup_url, json=signup_data)
        print(f"Signup Status: {response.status_code}")
        print(f"Signup Response: {response.text}")
        return response.status_code in [200, 201, 409]  # 409 = user already exists
    except Exception as e:
        print(f"Signup Error: {e}")
        return False

def test_login():
    """Test login with the test user"""
    login_url = "http://localhost:5000/api/auth/login"
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        print(f"Login Status: {response.status_code}")
        print(f"Login Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            return result.get('access_token')
        return None
    except Exception as e:
        print(f"Login Error: {e}")
        return None

def main():
    print("🔍 Checking User Authentication...")
    
    # Try to create a test user
    print("\n1. Testing signup...")
    signup_success = test_signup()
    
    # Try to login
    print("\n2. Testing login...")
    token = test_login()
    
    if token:
        print(f"✅ Authentication successful! Token: {token[:50]}...")
        return token
    else:
        print("❌ Authentication failed")
        return None

if __name__ == "__main__":
    main()
