#!/usr/bin/env python3
"""
Debug script to check what's actually stored in the saved recipes
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def get_auth_token():
    """Get authentication token"""
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                token = data.get('token')
                user_id = data.get('user', {}).get('id')
                return token, user_id
        return None, None
    except Exception as e:
        print(f"Login error: {e}")
        return None, None

def debug_saved_recipes():
    """Debug the saved recipes data structure"""
    print("🔍 Debugging Saved Recipes Data Structure")
    print("=" * 60)
    
    token, user_id = get_auth_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        return
    
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    # Get saved recipes and examine the structure
    print(f"\n1. Getting saved recipes for user: {user_id}")
    try:
        response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Response: {json.dumps(data, indent=2)}")
            
            if data.get('status') == 'success':
                recipes = data.get('recipes', [])
                print(f"\n   Found {len(recipes)} saved recipes:")
                
                for i, recipe in enumerate(recipes):
                    print(f"   Recipe {i+1}:")
                    print(f"     ID: {recipe.get('id')} (type: {type(recipe.get('id'))})")
                    print(f"     Name: {recipe.get('name')}")
                    print(f"     Keys: {list(recipe.keys())}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test with a specific recipe ID
    print(f"\n2. Testing save/unsave with a specific recipe...")
    
    # Get a recipe to test with
    try:
        response = requests.post(f"{BASE_URL}/api/recommend", 
                               json={"ingredients": ["chicken"]}, 
                               headers=headers)
        if response.status_code == 200:
            data = response.json()
            recipes = data.get('recipes', [])
            if recipes:
                test_recipe = recipes[0]
                recipe_id = test_recipe.get('id')
                recipe_name = test_recipe.get('name')
                print(f"   Test recipe: {recipe_name} (ID: {recipe_id}, type: {type(recipe_id)})")
                
                # Save it
                print(f"\n3. Saving recipe {recipe_id}...")
                save_response = requests.post(f"{BASE_URL}/api/recipe/{recipe_id}/save", headers=headers)
                print(f"   Save status: {save_response.status_code}")
                print(f"   Save response: {save_response.text}")
                
                # Check saved recipes again
                print(f"\n4. Checking saved recipes after save...")
                saved_response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
                if saved_response.status_code == 200:
                    saved_data = saved_response.json()
                    if saved_data.get('status') == 'success':
                        saved_recipes = saved_data.get('recipes', [])
                        print(f"   Found {len(saved_recipes)} saved recipes")
                        
                        # Look for our test recipe
                        found = False
                        for recipe in saved_recipes:
                            if str(recipe.get('id')) == str(recipe_id):
                                found = True
                                print(f"   ✅ Found test recipe in saved list")
                                print(f"      Saved ID: {recipe.get('id')} (type: {type(recipe.get('id'))})")
                                print(f"      Test ID: {recipe_id} (type: {type(recipe_id)})")
                                break
                        
                        if not found:
                            print(f"   ❌ Test recipe not found in saved list")
                            print(f"   Saved recipe IDs: {[str(r.get('id')) for r in saved_recipes]}")
                
                # Try to unsave it
                print(f"\n5. Unsaving recipe {recipe_id}...")
                unsave_response = requests.post(f"{BASE_URL}/api/recipe/{recipe_id}/unsave", headers=headers)
                print(f"   Unsave status: {unsave_response.status_code}")
                print(f"   Unsave response: {unsave_response.text}")
                
                # Check saved recipes after unsave
                print(f"\n6. Checking saved recipes after unsave...")
                final_response = requests.get(f"{BASE_URL}/api/recipes/saved", headers=headers)
                if final_response.status_code == 200:
                    final_data = final_response.json()
                    if final_data.get('status') == 'success':
                        final_recipes = final_data.get('recipes', [])
                        print(f"   Found {len(final_recipes)} saved recipes")
                        
                        # Check if our test recipe is still there
                        still_found = False
                        for recipe in final_recipes:
                            if str(recipe.get('id')) == str(recipe_id):
                                still_found = True
                                print(f"   ⚠️  Test recipe still in saved list!")
                                break
                        
                        if not still_found:
                            print(f"   ✅ Test recipe successfully removed from saved list")
    
    except Exception as e:
        print(f"   Error in test: {e}")

if __name__ == "__main__":
    debug_saved_recipes()
