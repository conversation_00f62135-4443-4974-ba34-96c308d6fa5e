#!/usr/bin/env python3
"""
Test server health and connectivity
"""

import requests

def test_server_health():
    print("Testing server health...")
    
    # Test different endpoints
    endpoints = [
        'http://localhost:5000/',
        'http://localhost:5000/api/test',
        'http://localhost:5000/api/health',
        'http://localhost:5000/health'
    ]
    
    for endpoint in endpoints:
        try:
            print(f"\nTesting {endpoint}...")
            response = requests.get(endpoint, timeout=5)
            print(f"  Status: {response.status_code}")
            print(f"  Content-Type: {response.headers.get('content-type', 'N/A')}")
            if response.headers.get('content-type', '').startswith('application/json'):
                print(f"  Response: {response.json()}")
            else:
                print(f"  Response: {response.text[:200]}...")
                
        except requests.exceptions.ConnectionError:
            print(f"  ❌ Connection refused - server not running on this endpoint")
        except requests.exceptions.Timeout:
            print(f"  ❌ Timeout - server not responding")
        except Exception as e:
            print(f"  ❌ Error: {e}")

if __name__ == "__main__":
    test_server_health()
