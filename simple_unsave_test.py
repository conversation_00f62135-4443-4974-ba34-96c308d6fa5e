#!/usr/bin/env python3
"""
Simple test to trigger the unsave debug logs
"""

import requests

BASE_URL = "http://localhost:5000"

def test_unsave():
    # Login
    login_data = {"email": "<EMAIL>", "password": "password123"}
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    data = response.json()
    token = data.get('token')
    
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    # Get a recipe to test with
    response = requests.post(f"{BASE_URL}/api/recommend", 
                           json={"ingredients": ["chicken"]}, 
                           headers=headers)
    recipes = response.json().get('recipes', [])
    recipe_id = recipes[0].get('id')
    
    print(f"Testing with recipe ID: {recipe_id}")
    
    # Save it first
    print("Saving recipe...")
    response = requests.post(f"{BASE_URL}/api/recipe/{recipe_id}/save", headers=headers)
    print(f"Save response: {response.status_code} - {response.text}")
    
    # Now unsave it
    print("Unsaving recipe...")
    response = requests.post(f"{BASE_URL}/api/recipe/{recipe_id}/unsave", headers=headers)
    print(f"Unsave response: {response.status_code} - {response.text}")

if __name__ == "__main__":
    test_unsave()
